'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { useRemixOnboarding } from '../remix-onboarding-context';
import Image from 'next/image';

export default function Step2() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      updateData({
        profilePhoto: file,
        profilePhotoUrl: url,
      });
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleContinue = () => {
    nextStep();
  };

  const handleBack = () => {
    prevStep();
  };

  return (
    <div className="space-y-6">
      {/* Title */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground font-arvo leading-tight">
          Choose a profile photo
        </h1>
      </div>

      {/* Upload Area */}
      <div className="space-y-5">
        {/* Drag & Drop Area */}
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          className={`border-2 border-dashed rounded-xl p-14 text-center transition-colors ${
            isDragOver
              ? 'border-primary bg-primary/5'
              : 'border-muted-foreground bg-transparent'
          }`}
        >
          {data.profilePhotoUrl ? (
            <div className="space-y-4">
              <div className="w-24 h-24 mx-auto rounded-full overflow-hidden">
                <Image
                  src={data.profilePhotoUrl}
                  alt="Profile preview"
                  width={96}
                  height={96}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-bold text-card-foreground font-arvo">
                  Photo uploaded successfully
                </h3>
                <p className="text-sm text-muted-foreground font-lato">
                  Click upload to change your photo
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <h3 className="text-lg font-bold text-card-foreground font-arvo">
                Add a profile photo
              </h3>
              <p className="text-sm text-muted-foreground font-lato">
                Drag and drop or click to upload
              </p>
            </div>
          )}
        </div>

        {/* Upload Button */}
        <div className="flex justify-center">
          <Button
            onClick={handleUploadClick}
            className="bg-primary text-primary-foreground font-arvo font-bold text-sm px-4 py-2.5 rounded-full hover:bg-primary/90"
          >
            Upload a photo
          </Button>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="px-6 py-3 font-arvo font-bold text-base border-muted-foreground text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          className="px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
