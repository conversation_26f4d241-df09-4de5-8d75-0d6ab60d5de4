import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/theme/theme-context";
import { I18nProvider } from "@/contexts/i18n/i18n-context";
import { MusicPlayerProvider } from "@/contexts/music-player-context/music-player-context"
import Header from "@/components/shared/header";
import { defaultLocale } from "@/i18n/config";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/side-bar/app-sidebar";
import AppGate from "@/components/auth/app-gate";
import { AuthProvider } from "@/contexts/auth/auth-context";
import Providers from "./providers";
import { MusicPlayer } from "@/components/shared/music-player/music-player";
import { MusicPlayerAwareLayout } from "@/components/shared/music-player-aware-layout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const arvo = Arvo({
  variable: "--font-arvo",
  subsets: ["latin"],
  weight: ["400", "700"],
});

const lato = Lato({
  variable: "--font-lato",
  subsets: ["latin"],
  weight: ["400", "700"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["400", "500"],
});

export const metadata: Metadata = {
  title: "Smash",
  description: "A modern music application with dark/light theme support",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // For static export, we use the default locale
  // Client-side locale switching will be handled by the LanguageToggle component
  const locale = defaultLocale;

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${arvo.variable} ${lato.variable} ${inter.variable} antialiased`}
      >
        <Providers>
          <ThemeProvider>
            <SidebarProvider>
              <AuthProvider>
                <AppGate>
                  <I18nProvider>
                    <MusicPlayerProvider>
                      <MusicPlayerAwareLayout>
                        <AppSidebar />
                        <div className="flex-1 flex flex-col h-full overflow-hidden">
                          <SidebarInset className="flex flex-col h-full overflow-hidden">
                            <Header />
                            <div className="flex-1 overflow-auto">
                              {children}
                            </div>
                            <MusicPlayer />
                          </SidebarInset>
                        </div>
                      </MusicPlayerAwareLayout>
                    </MusicPlayerProvider>
                  </I18nProvider>
                </AppGate>
              </AuthProvider>
            </SidebarProvider>
          </ThemeProvider>
        </Providers>
      </body>
    </html>
  );
}
