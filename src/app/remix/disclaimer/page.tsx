'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

export default function DisclaimerPage() {
  const [isAgreed, setIsAgreed] = useState(false);
  const router = useRouter();

  const handleAccept = () => {
    if (isAgreed) {
      router.push('/remix/onboarding');
    }
  };

  const handleCancel = () => {
    router.push('/remix');
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-background flex items-center justify-between px-4 md:px-16 py-4 border-b border-border">
        <div className="flex items-end gap-1">
          <div className="w-28 h-5 relative">
            <Image src="/SMASH-(full)-logo.png" alt="SMASH" fill className="object-contain" />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <button className="w-6 h-6 flex items-center justify-center">
            <Bell className="w-5 h-5 text-foreground" strokeWidth={1.5} />
          </button>
          <button className="w-6 h-6 flex items-center justify-center">
            <Settings className="w-6 h-6 text-foreground" strokeWidth={1.5} />
          </button>
          <Avatar className="w-10 h-10">
            <AvatarImage src="/remix/user-avatar.png" alt="User" />
            <AvatarFallback className="bg-background text-foreground font-medium">U</AvatarFallback>
          </Avatar>
        </div>
      </header>

      {/* Scrollable Content */}
      <main className="flex-1 overflow-y-auto px-4 md:px-16 py-8">
        <div className="max-w-2xl mx-auto space-y-6">
          
          {/* Title */}
          <div className="space-y-4">
            <h1 className="text-primary text-xl font-bold">
              Kesha &quot;Boy Crazy&quot; Remix Challenge: Official Disclaimer
            </h1>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              Please read this disclaimer carefully before participating in the Kesha &quot;Boy Crazy&quot; Remix Challenge (the &quot;Challenge&quot;). By submitting a remix, you agree to be bound by the terms and conditions outlined below.
            </p>
          </div>

          {/* Section 1 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              1. No Grant of Rights for Submission
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              By submitting your remix to the Challenge, you are not granting, Kesha Records or SMASH Music, license to your remix. If Kesha chooses your remix, Kesha Records will negotiate terms of the release in good faith.
            </p>
          </div>

          {/* Section 2 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              2. Originality and Clearances
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              You represent and warrant that your remix is an original work created solely by you, and that it does not infringe upon the copyrights, trademarks, privacy rights, publicity rights, or any other intellectual property or legal rights of any third party.
            </p>
          </div>

          {/* Section 3 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              3. No Compensation for Submission
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              Except for the potential opportunity described in Section 4, you acknowledge and agree that you will not receive any financial compensation or other remuneration for your submission to the Challenge or for the Kesha Parties&apos; use of your remix as described in Section 1.
            </p>
          </div>

          {/* Section 4 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              4. Potential Official Release
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              Kesha will review submitted remixes and may, at her sole discretion, select one remix for potential official release via Kesha Records. If your remix is selected, the terms of such official release, including but not limited to ownership, royalties, and all other commercial considerations, will be negotiated in good faith between you and Kesha Records. There is no guarantee that a mutually agreeable agreement will be reached, and Kesha Records reserves the right to withdraw any offer of release at any time prior to the execution of a definitive agreement.
            </p>
          </div>

          {/* Section 5 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              5. Ownership of Stems
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              The provided music stems for &quot;Boy Crazy&quot; remain the sole property of Kesha and/or her licensors. Your use of these stems is strictly limited to the creation of your remix for this Challenge and for no other purpose. You may not distribute, sell, or otherwise exploit the individual stems.
            </p>
          </div>

          {/* Section 6 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              6. No Obligation to Release
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              Kesha and Kesha Records are under no obligation to release any remix, even if one is selected. The decision to officially release a remix rests solely with Kesha Records.
            </p>
          </div>

          {/* Section 7 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              7. Indemnification
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              You agree to indemnify, defend, and hold harmless Kesha Parties from and against any and all claims, damages, liabilities, costs, and expenses (including reasonable attorneys&apos; fees) arising out of or in connection with your breach of any representation, warranty, or obligation under this disclaimer, or your participation in the Challenge.
            </p>
          </div>

          {/* Section 8 */}
          <div className="space-y-4">
            <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
              8. Governing Law
            </h2>
            <p className="text-base font-inter text-card-foreground leading-relaxed">
              This disclaimer shall be governed by and construed in accordance with the laws of the State of California, without regard to its conflict of law principles.
            </p>
          </div>

          {/* Section 9 with Checkbox */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h2 className="text-base font-inter font-semibold text-card-foreground leading-relaxed">
                9. Modifications
              </h2>
              <p className="text-base font-inter text-card-foreground leading-relaxed">
                Kesha reserves the right to modify or cancel the Challenge or these terms at any time, in her sole discretion, without prior notice.
              </p>
            </div>

            {/* Agreement Checkbox */}
            <div className="flex items-start gap-3 pt-4">
              <Checkbox
                id="disclaimer-agreement"
                checked={isAgreed}
                onCheckedChange={(checked) => setIsAgreed(checked as boolean)}
                className="mt-1 w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
              />
              <label htmlFor="disclaimer-agreement" className="text-base text-card-foreground cursor-pointer font-semibold font-lato leading-relaxed">
                By submitting your remix, you acknowledge that you have read, understood, and agree to this disclaimer.
              </label>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-border px-4 md:px-16 py-6">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-foreground text-lg font-semibold font-lato">By Smash Music</p>
          <div className="flex gap-4">
            <Button
              onClick={handleCancel}
              variant="outline"
              className="px-6 py-2"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAccept}
              disabled={!isAgreed}
              className="px-6 py-2 bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
            >
              Accept & Continue
            </Button>
          </div>
        </div>
      </footer>
    </div>
  );
}
